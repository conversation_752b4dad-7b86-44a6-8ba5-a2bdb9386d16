using System;
using System.Windows;
using Avalonia;
using RevitAvalonia.UI.Views;

namespace RevitAvalonia.UI;

public partial class RevitAvaloniaWindow : Window
{
    private static bool _avaloniaInitialized = false;

    public RevitAvaloniaWindow()
    {
        InitializeAvalonia();
        InitializeComponent();
        SetupAvaloniaContent();
    }

    private static void InitializeAvalonia()
    {
        if (_avaloniaInitialized) return;

        try
        {
            // Simple Avalonia initialization for embedding scenarios
            AppBuilder.Configure(() => new Avalonia.Application())
                .UsePlatformDetect()
                .SetupWithoutStarting();

            _avaloniaInitialized = true;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to initialize Avalonia: {ex.Message}", "Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SetupAvaloniaContent()
    {
        try
        {
            AvaloniaHost.Content = new RevitMainView();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to setup Avalonia content: {ex.Message}", "Error", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
