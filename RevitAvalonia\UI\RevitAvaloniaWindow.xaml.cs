using System;
using System.IO;
using System.Reflection;
using System.Windows;
using Avalonia;
using Avalonia.Platform;
using Avalonia.Win32;
using RevitAvalonia.UI.Views;

namespace RevitAvalonia.UI;

public partial class RevitAvaloniaWindow : Window
{
    private static bool _avaloniaInitialized = false;
    private static string _initializationError = string.Empty;

    public RevitAvaloniaWindow()
    {
        InitializeAvalonia();
        InitializeComponent();
        SetupAvaloniaContent();
    }

    private static void InitializeAvalonia()
    {
        if (_avaloniaInitialized) return;

        try
        {
            // Use the robust initializer designed for Revit environment
            if (AvaloniaInitializer.Initialize())
            {
                _avaloniaInitialized = true;
                System.Diagnostics.Debug.WriteLine("Avalonia successfully initialized for Revit");
            }
            else
            {
                throw new InvalidOperationException($"Avalonia initialization failed: {AvaloniaInitializer.LastError}");
            }
        }
        catch (Exception ex)
        {
            _initializationError = ex.ToString();

            // Generate diagnostic report for troubleshooting
            DiagnosticHelper.SaveDiagnosticReport();
            var diagnosticReport = DiagnosticHelper.GenerateDiagnosticReport();

            // Show detailed error information for troubleshooting
            var errorMessage = $"Failed to initialize Avalonia in Revit environment.\n\n" +
                              $"Error: {ex.Message}\n\n" +
                              $"Common causes:\n" +
                              $"• Graphics context conflicts with Revit's OpenGL/DirectX\n" +
                              $"• Missing SkiaSharp native dependencies (libSkiaSharp.dll)\n" +
                              $"• Incompatible rendering backend\n" +
                              $"• Assembly loading conflicts\n\n" +
                              $"A diagnostic report has been saved to your temp folder.\n\n" +
                              $"Detailed error:\n{ex}";

            MessageBox.Show(errorMessage, "Avalonia Initialization Error",
                MessageBoxButton.OK, MessageBoxImage.Error);

            // Also show diagnostic info in debug output
            System.Diagnostics.Debug.WriteLine("=== AVALONIA INITIALIZATION FAILURE ===");
            System.Diagnostics.Debug.WriteLine(diagnosticReport);
            System.Diagnostics.Debug.WriteLine("=== END DIAGNOSTIC REPORT ===");
        }
    }



    private void SetupAvaloniaContent()
    {
        try
        {
            if (!_avaloniaInitialized)
            {
                MessageBox.Show($"Avalonia was not properly initialized. Error: {_initializationError}",
                    "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            AvaloniaHost.Content = new RevitMainView();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to setup Avalonia content: {ex.Message}\n\nStack trace: {ex.StackTrace}",
                "Content Setup Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
