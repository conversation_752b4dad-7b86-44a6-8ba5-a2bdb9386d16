using Autodesk.Revit.Attributes;
using Nice3point.Revit.Toolkit.External;
using RevitAvalonia.UI;

namespace RevitAvalonia.Commands;

/// <summary>
///     External command entry point
/// </summary>
[UsedImplicitly]
[Transaction(TransactionMode.Manual)]
public class StartupCommand : ExternalCommand
{
    public override void Execute()
    {
        try
        {
            // Set the Revit context for the UI
            RevitContext.UiApplication = UiApplication;

            // Create and show the Avalonia window
            var window = new RevitAvaloniaWindow();
            window.Show();
        }
        catch (System.Exception ex)
        {
            Autodesk.Revit.UI.TaskDialog.Show("Error",
                $"Failed to launch Avalonia window: {ex.Message}");
        }
    }
}