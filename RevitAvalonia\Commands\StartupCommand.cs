﻿using Autodesk.Revit.Attributes;
using Nice3point.Revit.Toolkit.External;
using RevitAvalonia.UI;

namespace RevitAvalonia.Commands;

/// <summary>
///     External command entry point
/// </summary>
[UsedImplicitly]
[Transaction(TransactionMode.Manual)]
public class StartupCommand : ExternalCommand
{
    public override void Execute()
    {
        try
        {
            // Set the Revit context for the UI
            RevitContext.UiApplication = UiApplication;

            // Try to initialize and show the Avalonia window
            if (TryShowAvaloniaWindow())
            {
                return; // Success - Avalonia window is shown
            }

            // Fallback to WPF window if Avalonia fails
            ShowFallbackWindow();
        }
        catch (System.Exception ex)
        {
            Autodesk.Revit.UI.TaskDialog.Show("Error",
                $"Failed to launch add-in window: {ex.Message}\n\nStack trace:\n{ex.StackTrace}");
        }
    }

    private bool TryShowAvaloniaWindow()
    {
        try
        {
            var window = new RevitAvaloniaWindow();
            window.Show();
            return true;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Avalonia window failed: {ex}");
            return false;
        }
    }

    private void ShowFallbackWindow()
    {
        try
        {
            var fallbackWindow = new FallbackWindow();
            fallbackWindow.Show();

            // Notify user about fallback mode
            Autodesk.Revit.UI.TaskDialog.Show("Add-in Launched",
                "The add-in is running in fallback mode using WPF.\n\n" +
                "Avalonia UI initialization failed, but core functionality is available.\n\n" +
                "Use the 'Generate Diagnostic Report' button in the window to troubleshoot the issue.");
        }
        catch (System.Exception ex)
        {
            throw new System.InvalidOperationException("Both Avalonia and WPF fallback failed", ex);
        }
    }
}