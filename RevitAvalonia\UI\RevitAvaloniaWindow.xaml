<Window x:Class="RevitAvalonia.UI.RevitAvaloniaWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:av="clr-namespace:AvaloniaHwndHost;assembly=AvaloniaHwndHost"
        Title="Revit Avalonia Add-in" 
        Height="700" 
        Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    
    <Grid>
        <av:AvaloniaHwndHost x:Name="AvaloniaHost" />
    </Grid>
    
</Window>
