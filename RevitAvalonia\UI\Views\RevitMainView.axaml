<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="600"
             x:Class="RevitAvalonia.UI.Views.RevitMainView">
  
  <Grid>
    <ScrollViewer>
      <StackPanel Margin="20" Spacing="15">
        
        <!-- Header -->
        <Border Background="#2E3440" CornerRadius="8" Padding="15">
          <StackPanel Spacing="5">
            <TextBlock Text="Revit Avalonia Add-in" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Foreground="White"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="Powered by Avalonia UI 11.3.2"
                       FontSize="12"
                       Foreground="#D8DEE9"
                       HorizontalAlignment="Center"/>
          </StackPanel>
        </Border>
        
        <!-- Status Section -->
        <Border Background="#ECEFF4" CornerRadius="5" Padding="15">
          <StackPanel Spacing="10">
            <TextBlock Text="Status Information" FontWeight="Bold" FontSize="16"/>
            <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto" RowSpacing="5">
              <TextBlock Grid.Row="0" Grid.Column="0" Text="Revit Version:" Margin="0,0,10,0"/>
              <TextBlock Grid.Row="0" Grid.Column="1" x:Name="RevitVersionText" Text="Loading..." Foreground="#5E81AC"/>
              
              <TextBlock Grid.Row="1" Grid.Column="0" Text="Document:" Margin="0,0,10,0"/>
              <TextBlock Grid.Row="1" Grid.Column="1" x:Name="DocumentText" Text="Loading..." Foreground="#5E81AC"/>
              
              <TextBlock Grid.Row="2" Grid.Column="0" Text="Status:" Margin="0,0,10,0"/>
              <TextBlock Grid.Row="2" Grid.Column="1" x:Name="StatusText" Text="Ready" Foreground="#A3BE8C"/>
            </Grid>
          </StackPanel>
        </Border>
        
        <!-- Actions Section -->
        <Border Background="#ECEFF4" CornerRadius="5" Padding="15">
          <StackPanel Spacing="10">
            <TextBlock Text="Actions" FontWeight="Bold" FontSize="16"/>
            
            <Button Content="Get Document Info" 
                    Click="OnGetDocumentInfoClick"
                    Background="#5E81AC"
                    Foreground="White"
                    Padding="15,8"
                    CornerRadius="4"/>
            
            <Button Content="Count Elements" 
                    Click="OnCountElementsClick"
                    Background="#81A1C1"
                    Foreground="White"
                    Padding="15,8"
                    CornerRadius="4"/>
            
            <Button Content="Show Message" 
                    Click="OnShowMessageClick"
                    Background="#88C0D0"
                    Foreground="White"
                    Padding="15,8"
                    CornerRadius="4"/>
          </StackPanel>
        </Border>
        
        <!-- Settings Section -->
        <Border Background="#ECEFF4" CornerRadius="5" Padding="15">
          <StackPanel Spacing="10">
            <TextBlock Text="Settings" FontWeight="Bold" FontSize="16"/>
            
            <CheckBox Content="Enable Auto-refresh" IsChecked="True"/>
            <CheckBox Content="Show detailed logs"/>
            <CheckBox Content="Use metric units"/>
            
            <StackPanel Orientation="Horizontal" Spacing="10">
              <TextBlock Text="Refresh Interval:" VerticalAlignment="Center"/>
              <Slider Minimum="1" Maximum="10" Value="5" Width="150" VerticalAlignment="Center"/>
              <TextBlock Text="5s" VerticalAlignment="Center"/>
            </StackPanel>
          </StackPanel>
        </Border>
        
        <!-- Log Section -->
        <Border Background="#ECEFF4" CornerRadius="5" Padding="15">
          <StackPanel Spacing="10">
            <TextBlock Text="Activity Log" FontWeight="Bold" FontSize="16"/>
            <ScrollViewer Height="150" Background="White" CornerRadius="3">
              <TextBlock x:Name="LogText" 
                         Text="Add-in initialized successfully.&#x0A;Ready for operations."
                         Margin="10"
                         FontFamily="Consolas"
                         FontSize="11"
                         Foreground="#2E3440"/>
            </ScrollViewer>
            <Button Content="Clear Log" 
                    Click="OnClearLogClick"
                    Background="#BF616A"
                    Foreground="White"
                    Padding="10,5"
                    CornerRadius="3"
                    HorizontalAlignment="Right"/>
          </StackPanel>
        </Border>
        
      </StackPanel>
    </ScrollViewer>
  </Grid>
  
</UserControl>
