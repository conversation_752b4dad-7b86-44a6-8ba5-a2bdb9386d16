using System.Windows;
using Avalonia;
using Avalonia.ReactiveUI;
using DesktopUI2.ViewModels;
using DesktopUI2.Views;

namespace DesktopUI2.WPF;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
  public MainWindow()
  {
    BuildAvaloniaApp().SetupWithoutStarting();
    InitializeComponent();

    AvaloniaHost.Content = new MainUserControl();
  }

  public static AppBuilder BuildAvaloniaApp()
  {
    return AppBuilder
      .Configure<DesktopUI2.App>()
      .With(new SkiaOptions { MaxGpuResourceSizeBytes = 8096000 })
      .With(new Win32PlatformOptions())
      .LogToTrace();
  }
}
