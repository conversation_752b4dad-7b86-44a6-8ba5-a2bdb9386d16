using System;
using System.IO;
using System.Windows;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;

namespace RevitAvalonia.UI;

public partial class FallbackWindow : Window
{
    public FallbackWindow()
    {
        InitializeComponent();
        UpdateRevitInfo();
    }

    private void UpdateRevitInfo()
    {
        try
        {
            var uiApp = RevitContext.UiApplication;
            if (uiApp != null)
            {
                RevitVersionText.Text = uiApp.Application.VersionName;

                var doc = uiApp.ActiveUIDocument?.Document;
                DocumentText.Text = doc?.Title ?? "No active document";
            }
            else
            {
                RevitVersionText.Text = "Not available";
                DocumentText.Text = "Not available";
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error updating Revit info: {ex.Message}");
        }
    }

    private void OnGetDocumentInfoClick(object sender, RoutedEventArgs e)
    {
        try
        {
            var uiApp = RevitContext.UiApplication;
            var doc = uiApp?.ActiveUIDocument?.Document;
            
            if (doc == null)
            {
                LogMessage("No active document found.");
                return;
            }

            var info = $"Document: {doc.Title}\n" +
                      $"Path: {doc.PathName}\n" +
                      $"Is Modified: {doc.IsModified}\n" +
                      $"Units: {doc.GetUnits().GetFormatOptions(SpecTypeId.Length).GetUnitTypeId()}";
            
            LogMessage($"Document Info:\n{info}");
            StatusText.Text = "Document info retrieved";
        }
        catch (Exception ex)
        {
            LogMessage($"Error getting document info: {ex.Message}");
        }
    }

    private void OnCountElementsClick(object sender, RoutedEventArgs e)
    {
        try
        {
            var uiApp = RevitContext.UiApplication;
            var doc = uiApp?.ActiveUIDocument?.Document;
            
            if (doc == null)
            {
                LogMessage("No active document found.");
                return;
            }

            var collector = new FilteredElementCollector(doc);
            var allElements = collector.WhereElementIsNotElementType().ToElements();
            
            LogMessage($"Total elements in document: {allElements.Count}");
            StatusText.Text = $"Found {allElements.Count} elements";
        }
        catch (Exception ex)
        {
            LogMessage($"Error counting elements: {ex.Message}");
        }
    }

    private void OnShowMessageClick(object sender, RoutedEventArgs e)
    {
        try
        {
            TaskDialog.Show("Revit Add-in (Fallback Mode)", 
                "Hello from WPF fallback interface!\n\n" +
                "This demonstrates that the Revit integration works,\n" +
                "even when Avalonia UI initialization fails.\n\n" +
                "Features available:\n" +
                "• Revit API access\n" +
                "• Document operations\n" +
                "• Element queries\n" +
                "• Basic UI functionality");
            
            LogMessage("Message dialog shown successfully.");
            StatusText.Text = "Message displayed";
        }
        catch (Exception ex)
        {
            LogMessage($"Error showing message: {ex.Message}");
        }
    }

    private void OnGenerateDiagnosticClick(object sender, RoutedEventArgs e)
    {
        try
        {
            var tempPath = Path.GetTempPath();
            var fileName = $"AvaloniaRevitDiagnostic_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            var filePath = Path.Combine(tempPath, fileName);
            
            DiagnosticHelper.SaveDiagnosticReport(filePath);
            
            LogMessage($"Diagnostic report generated: {filePath}");
            StatusText.Text = "Diagnostic report generated";
            
            // Ask if user wants to open the file
            var result = System.Windows.MessageBox.Show(
                $"Diagnostic report saved to:\n{filePath}\n\nWould you like to open it?",
                "Diagnostic Report Generated",
                MessageBoxButton.YesNo,
                MessageBoxImage.Information);
                
            if (result == MessageBoxResult.Yes)
            {
                System.Diagnostics.Process.Start("notepad.exe", filePath);
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error generating diagnostic report: {ex.Message}");
        }
    }

    private void OnClearLogClick(object sender, RoutedEventArgs e)
    {
        LogTextBox.Text = "Log cleared.";
        StatusText.Text = "Log cleared";
    }

    private void LogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        var currentText = LogTextBox.Text ?? "";
        LogTextBox.Text = $"{currentText}\n[{timestamp}] {message}";
        
        // Auto-scroll to bottom
        LogTextBox.ScrollToEnd();
    }
}
