﻿using Nice3point.Revit.Toolkit.External;
using RevitAvalonia.Commands;

namespace RevitAvalonia;

/// <summary>
///     Application entry point
/// </summary>
[UsedImplicitly]
public class Application : ExternalApplication
{
    public override void OnStartup()
    {
        Host.Start();
        CreateRibbon();
    }

    public override void OnShutdown()
    {
        Host.Stop();
    }

    private void CreateRibbon()
    {
        var panel = Application.CreatePanel("Commands", "RevitAvalonia");

        panel.AddPushButton<StartupCommand>("Execute")
            .SetImage("/RevitAvalonia;component/Resources/Icons/RibbonIcon16.png")
            .SetLargeImage("/RevitAvalonia;component/Resources/Icons/RibbonIcon32.png");
    }
}