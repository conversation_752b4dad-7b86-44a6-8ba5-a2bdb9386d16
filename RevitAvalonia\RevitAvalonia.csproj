﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <UseWPF>true</UseWPF>
        <Nullable>enable</Nullable>
        <LangVersion>latest</LangVersion>
        <PlatformTarget>x64</PlatformTarget>
        <ImplicitUsings>true</ImplicitUsings>
        <EnableDynamicLoading>true</EnableDynamicLoading>
        <Configurations>Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Debug R26</Configurations>
        <Configurations>$(Configurations);Release R21;Release R22;Release R23;Release R24;Release R25;Release R26</Configurations>
    </PropertyGroup>

    <!-- Revit configuration -->
    <PropertyGroup Condition="$(Configuration.Contains('R21'))">
        <RevitVersion>2021</RevitVersion>
        <TargetFramework>net48</TargetFramework>
    </PropertyGroup>
    <PropertyGroup Condition="$(Configuration.Contains('R22'))">
        <RevitVersion>2022</RevitVersion>
        <TargetFramework>net48</TargetFramework>
    </PropertyGroup>
    <PropertyGroup Condition="$(Configuration.Contains('R23'))">
        <RevitVersion>2023</RevitVersion>
        <TargetFramework>net48</TargetFramework>
    </PropertyGroup>
    <PropertyGroup Condition="$(Configuration.Contains('R24'))">
        <RevitVersion>2024</RevitVersion>
        <TargetFramework>net48</TargetFramework>
    </PropertyGroup>
    <PropertyGroup Condition="$(Configuration.Contains('R25'))">
        <RevitVersion>2025</RevitVersion>
        <TargetFramework>net8.0-windows</TargetFramework>
    </PropertyGroup>
    <PropertyGroup Condition="$(Configuration.Contains('R26'))">
        <RevitVersion>2026</RevitVersion>
        <TargetFramework>net8.0-windows</TargetFramework>
    </PropertyGroup>

    <!-- Launch configuration -->
    <PropertyGroup>
        <StartAction>Program</StartAction>
        <StartProgram>C:\Program Files\Autodesk\Revit $(RevitVersion)\Revit.exe</StartProgram>
        <StartArguments>/language ENG</StartArguments>
    </PropertyGroup>

    <!-- Build configuration -->
    <!-- https://github.com/Nice3point/Revit.Build.Tasks -->
    <PropertyGroup>
        <DeployRevitAddin>true</DeployRevitAddin>
    </PropertyGroup>

    <ItemGroup>
        <!-- Revit References -->
        <PackageReference Include="Avalonia" Version="11.3.2" />
        <PackageReference Include="Avalonia.Desktop" Version="11.3.2" />
        <PackageReference Include="Nice3point.Revit.Build.Tasks" Version="3.0.1"/>
        <PackageReference Include="Nice3point.Revit.Toolkit" Version="$(RevitVersion).*"/>
        <PackageReference Include="Nice3point.Revit.Extensions" Version="$(RevitVersion).*"/>
        <PackageReference Include="Nice3point.Revit.Api.RevitAPI" Version="$(RevitVersion).*"/>
        <PackageReference Include="Nice3point.Revit.Api.RevitAPIUI" Version="$(RevitVersion).*"/>

        <!-- MVVM -->
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0"/>

        <!-- IOC -->
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.3"/>

        <!-- Logging -->
        <PackageReference Include="Serilog" Version="4.2.0"/>
        <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0"/>
        <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0"/>
    </ItemGroup>

    <ItemGroup>
        <Resource Include="Resources\Icons\RibbonIcon16.png"/>
        <Resource Include="Resources\Icons\RibbonIcon32.png"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\AvaloniaHwndHost\AvaloniaHwndHost.csproj" />
    </ItemGroup>

</Project>