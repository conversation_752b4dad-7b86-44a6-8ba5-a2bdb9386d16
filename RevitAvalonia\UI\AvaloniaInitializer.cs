using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using Avalonia;
using Avalonia.Platform;
using Avalonia.Win32;

namespace RevitAvalonia.UI;

/// <summary>
/// Specialized Avalonia initializer for Revit host environment
/// <PERSON>les graphics context conflicts and native library loading issues
/// </summary>
public static class AvaloniaInitializer
{
    private static bool _initialized = false;
    private static readonly object _lock = new object();
    private static string _lastError = string.Empty;

    public static bool IsInitialized => _initialized;
    public static string LastError => _lastError;

    /// <summary>
    /// Initialize Avalonia with multiple fallback strategies optimized for Revit
    /// </summary>
    public static bool Initialize()
    {
        lock (_lock)
        {
            if (_initialized) return true;

            try
            {
                // Log system information for diagnostics
                LogSystemInfo();

                // Preload native dependencies
                PreloadNativeDependencies();

                // Try initialization strategies in order of preference
                if (TryInitializeWithSoftwareRendering() ||
                    TryInitializeWithWin32Skia() ||
                    TryInitializeWithPlatformDetect() ||
                    TryInitializeMinimal())
                {
                    _initialized = true;
                    Debug.WriteLine("Avalonia initialized successfully for Revit");
                    return true;
                }

                throw new InvalidOperationException("All Avalonia initialization strategies failed");
            }
            catch (Exception ex)
            {
                _lastError = ex.ToString();
                Debug.WriteLine($"Avalonia initialization failed: {ex}");
                return false;
            }
        }
    }

    private static void LogSystemInfo()
    {
        try
        {
            Debug.WriteLine($"OS: {Environment.OSVersion}");
            Debug.WriteLine($"CLR: {Environment.Version}");
            Debug.WriteLine($"Process: {Environment.Is64BitProcess} bit");
            Debug.WriteLine($"Assembly location: {Assembly.GetExecutingAssembly().Location}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Failed to log system info: {ex.Message}");
        }
    }

    private static void PreloadNativeDependencies()
    {
        try
        {
            var assemblyDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            if (string.IsNullOrEmpty(assemblyDir)) return;

            // Try to preload SkiaSharp native library
            var possiblePaths = new[]
            {
                Path.Combine(assemblyDir, "runtimes", "win-x64", "native", "libSkiaSharp.dll"),
                Path.Combine(assemblyDir, "runtimes", "win", "native", "libSkiaSharp.dll"),
                Path.Combine(assemblyDir, "libSkiaSharp.dll"),
                Path.Combine(assemblyDir, "SkiaSharp.dll")
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    try
                    {
                        var handle = NativeMethods.LoadLibrary(path);
                        if (handle != IntPtr.Zero)
                        {
                            Debug.WriteLine($"Successfully preloaded: {path}");
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Failed to preload {path}: {ex.Message}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Preload dependencies failed: {ex.Message}");
        }
    }

    private static bool TryInitializeWithSoftwareRendering()
    {
        try
        {
            Debug.WriteLine("Trying software rendering initialization...");
            
            AppBuilder.Configure(() => new Avalonia.Application())
                .UseWin32()
                .UseSkia()
                .SetupWithoutStarting();

            Debug.WriteLine("Software rendering initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Software rendering failed: {ex.Message}");
            return false;
        }
    }

    private static bool TryInitializeWithWin32Skia()
    {
        try
        {
            Debug.WriteLine("Trying Win32 + Skia initialization...");
            
            AppBuilder.Configure(() => new Avalonia.Application())
                .UseWin32()
                .UseSkia()
                .SetupWithoutStarting();

            Debug.WriteLine("Win32 + Skia initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Win32 + Skia failed: {ex.Message}");
            return false;
        }
    }

    private static bool TryInitializeWithPlatformDetect()
    {
        try
        {
            Debug.WriteLine("Trying platform detect initialization...");
            
            AppBuilder.Configure(() => new Avalonia.Application())
                .UsePlatformDetect()
                .SetupWithoutStarting();

            Debug.WriteLine("Platform detect initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Platform detect failed: {ex.Message}");
            return false;
        }
    }

    private static bool TryInitializeMinimal()
    {
        try
        {
            Debug.WriteLine("Trying minimal initialization...");
            
            AppBuilder.Configure(() => new Avalonia.Application())
                .UseWin32()
                .SetupWithoutStarting();

            Debug.WriteLine("Minimal initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Minimal initialization failed: {ex.Message}");
            return false;
        }
    }
}
