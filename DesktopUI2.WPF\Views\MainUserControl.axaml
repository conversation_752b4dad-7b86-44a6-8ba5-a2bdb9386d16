<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="750"
             x:Class="DesktopUI2.Views.MainUserControl">
  
  <Grid>
    <StackPanel Margin="20" Spacing="10">
      <TextBlock Text="Avalonia UI in WPF Host" 
                 FontSize="24" 
                 FontWeight="Bold" 
                 HorizontalAlignment="Center"/>
      
      <TextBlock Text="This is an Avalonia UserControl embedded in a WPF application using AvaloniaHwndHost."
                 TextWrapping="Wrap"
                 HorizontalAlignment="Center"
                 Margin="0,10"/>
      
      <Button Content="Click Me!" 
              HorizontalAlignment="Center"
              Padding="20,10"
              Click="OnButtonClick"/>
      
      <TextBlock x:Name="StatusText" 
                 Text="Ready"
                 HorizontalAlignment="Center"
                 Foreground="Green"
                 Margin="0,10"/>
      
      <Border Background="LightBlue" 
              CornerRadius="5" 
              Padding="15"
              Margin="0,20">
        <StackPanel Spacing="5">
          <TextBlock Text="Sample Controls:" FontWeight="Bold"/>
          <CheckBox Content="Enable Feature A" IsChecked="True"/>
          <CheckBox Content="Enable Feature B"/>
          <Slider Minimum="0" Maximum="100" Value="50" Width="200"/>
          <TextBox Watermark="Enter some text..." Width="200"/>
        </StackPanel>
      </Border>
    </StackPanel>
  </Grid>
  
</UserControl>
