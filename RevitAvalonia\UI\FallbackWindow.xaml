<Window x:Class="RevitAvalonia.UI.FallbackWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Revit Add-in (Fallback Mode)" 
        Height="500" 
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2E3440" CornerRadius="5" Padding="15" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="Revit Add-in (WPF Fallback)" 
                           FontSize="18" 
                           FontWeight="Bold" 
                           Foreground="White"
                           HorizontalAlignment="Center"/>
                <TextBlock Text="Avalonia UI initialization failed - using WPF fallback"
                           FontSize="11"
                           Foreground="#D8DEE9"
                           HorizontalAlignment="Center"
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Status Section -->
                <GroupBox Header="Status Information" Padding="10" Margin="0,0,0,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Revit Version:" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="RevitVersionText" Text="Loading..." Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Document:" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="DocumentText" Text="Loading..." Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Status:" Margin="0,0,10,0"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="StatusText" Text="Ready" Foreground="Green"/>
                    </Grid>
                </GroupBox>

                <!-- Actions Section -->
                <GroupBox Header="Actions" Padding="10" Margin="0,0,0,15">
                    <StackPanel>
                        <Button Content="Get Document Info" 
                                Click="OnGetDocumentInfoClick"
                                Margin="0,0,0,10"
                                Padding="10,5"/>
                        
                        <Button Content="Count Elements" 
                                Click="OnCountElementsClick"
                                Margin="0,0,0,10"
                                Padding="10,5"/>
                        
                        <Button Content="Show Message" 
                                Click="OnShowMessageClick"
                                Margin="0,0,0,10"
                                Padding="10,5"/>
                        
                        <Button Content="Generate Diagnostic Report" 
                                Click="OnGenerateDiagnosticClick"
                                Padding="10,5"
                                Background="Orange"
                                Foreground="White"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- Log Section -->
                <GroupBox Header="Activity Log" Padding="10" Margin="0,0,0,0">
                    <StackPanel>
                        <TextBox x:Name="LogTextBox" 
                                 Height="150" 
                                 IsReadOnly="True"
                                 VerticalScrollBarVisibility="Auto"
                                 FontFamily="Consolas"
                                 FontSize="10"
                                 Text="Add-in initialized in fallback mode.&#x0A;Avalonia UI initialization failed - using WPF instead."/>
                        <Button Content="Clear Log" 
                                Click="OnClearLogClick"
                                HorizontalAlignment="Right"
                                Margin="0,5,0,0"
                                Padding="10,3"/>
                    </StackPanel>
                </GroupBox>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer -->
        <TextBlock Grid.Row="2" 
                   Text="This is a WPF fallback interface. For full functionality, resolve Avalonia initialization issues."
                   FontSize="10"
                   Foreground="Gray"
                   TextWrapping="Wrap"
                   HorizontalAlignment="Center"
                   Margin="0,10,0,0"/>
    </Grid>
    
</Window>
