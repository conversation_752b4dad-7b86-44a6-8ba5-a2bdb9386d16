using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;

namespace DesktopUI2.Views;

public partial class MainUserControl : UserControl
{
    private TextBlock? _statusText;
    private int _clickCount = 0;

    public MainUserControl()
    {
        InitializeComponent();
    }

    private void InitializeComponent()
    {
        AvaloniaXamlLoader.Load(this);
        _statusText = this.FindControl<TextBlock>("StatusText");
    }

    private void OnButtonClick(object? sender, RoutedEventArgs e)
    {
        _clickCount++;
        if (_statusText != null)
        {
            _statusText.Text = $"Button clicked {_clickCount} time{(_clickCount == 1 ? "" : "s")}!";
        }
    }
}
